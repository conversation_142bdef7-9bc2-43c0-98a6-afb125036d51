/* Pre-login UI styles - <PERSON>, <PERSON>, <PERSON> interface */
:root {
    --bg-primary: #FFFFFF;
    --text-primary: #000000;
    --text-secondary: #666666;
    --border-color: #E5E5E5;
    --accent-color: #333333;
    --accent-hover: #555555;
    --button-bg: #000000;
    --button-text: #FFFFFF;
    --button-hover-bg: #333333;
    --input-bg: #FFFFFF;
    --input-border: #E5E5E5;
    --input-text: #000000;
    --input-placeholder: #999999;
}

/* --- GLOBAL: box-sizing and overflow-x --- */
html, body, .pre-login-container, .pre-login-main, .pre-login-welcome-message, .pre-login-bottom-input-container {
  box-sizing: border-box;
  overflow-x: hidden;
  width: 100vw;
  max-width: 100vw;
}
* {
  box-sizing: inherit;
}

/* --- MAIN LAYOUT: Flex/grid for robust stacking --- */
.pre-login-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100vw;
  max-width: 100vw;
  overflow-x: hidden;
  background-color: var(--bg-primary);
}

.pre-login-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid var(--border-color);
}

.pre-login-logo {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 700;
    font-size: 20px;
    color: var(--text-primary);
}

.pre-login-logo i {
    font-size: 22px;
    color: var(--accent-color);
}

.pre-login-logo span {
    display: inline-block;
    letter-spacing: 0.5px;
}

.pre-login-logo img {
    height: 24px;
}

.pre-login-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.login-btn {
    background-color: var(--button-bg);
    color: var(--button-text);
    border: none;
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.login-btn:hover {
    background-color: var(--button-hover-bg);
}

.signup-btn {
    background-color: transparent;
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s, border-color 0.2s;
}

.signup-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
    border-color: var(--text-secondary);
}

.pre-login-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  position: relative;
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
}

/* --- WELCOME MESSAGE: Responsive centering and scaling --- */
.pre-login-welcome-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  position: fixed;
  top: 35%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  transition: all 0.3s ease-out;
  margin-bottom: 40px;
}

.pre-login-welcome-message {
  background-color: var(--bg-primary);
  border-radius: 12px;
  padding: 28px;
  max-width: 700px;
  width: 90%;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  text-align: center;
  border: 1px solid var(--border-color);
  transition: transform 0.3s ease-out;
}

.pre-login-welcome-message h2 {
    margin-bottom: 16px;
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 700;
}

.pre-login-welcome-message p {
    margin-bottom: 24px;
    color: var(--text-secondary);
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
}

/* --- SUGGESTION CHIPS: Wrap and scale --- */
.pre-login-suggestion-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
}

.pre-login-suggestion-chip {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  color: var(--text-primary);
  cursor: pointer;
  transition: background-color 0.2s, border-color 0.2s;
}

.pre-login-suggestion-chip:hover {
    background-color: rgba(0, 0, 0, 0.05);
    border-color: var(--text-secondary);
}

/* --- INPUT AREA: Always visible, never overflows --- */
.pre-login-bottom-input-container {
  width: 100%;
  max-width: 768px;
  margin-top: auto;
  position: fixed;
  top: 65%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 0 24px;
  z-index: 10;
  margin-top: 40px;
}

/* ChatGPT-style input wrapper for pre-login - Override all previous styles */
.pre-login-chat-input-form {
    width: 100% !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    margin: 0 !important;
    padding: 0 !important;
}

.pre-login-bottom-input-container .chatgpt-input-wrapper {
    background: var(--input-bg) !important;
    border: 1px solid var(--input-border) !important;
    border-radius: 24px !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05) !important;
    padding: 16px !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 12px !important;
    transition: all 0.2s ease !important;
    overflow: visible !important;
}

.pre-login-bottom-input-container .chatgpt-input-area {
    width: 100% !important;
    order: 1 !important;
}

.pre-login-bottom-input-container .chatgpt-input-area textarea {
    width: 100% !important;
    border: none !important;
    outline: none !important;
    background: transparent !important;
    resize: none !important;
    font-size: 16px !important;
    padding: 0 !important;
    min-height: 24px !important;
    max-height: 120px !important;
    color: var(--input-text) !important;
    font-family: inherit !important;
    line-height: 1.5 !important;
}

.pre-login-bottom-input-container .chatgpt-input-area textarea::placeholder {
    color: var(--input-placeholder) !important;
    font-size: 16px !important;
}

.pre-login-bottom-input-container .chatgpt-tools-row {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    width: 100% !important;
    order: 2 !important;
}

.pre-login-bottom-input-container .chatgpt-left-tools,
.pre-login-bottom-input-container .chatgpt-right-tools {
    display: flex;
    align-items: center;
    gap: 8px;
}

.pre-login-bottom-input-container .chatgpt-tool-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    cursor: pointer;
    padding: 0;
    font-size: 14px;
}

.pre-login-bottom-input-container .chatgpt-tool-btn:hover {
    background: rgba(0, 0, 0, 0.05);
    color: var(--text-primary);
}

.pre-login-bottom-input-container .chatgpt-send-btn {
    background: #ececec;
    border: none;
    color: #b0b0b0;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s, color 0.2s;
    cursor: pointer;
    padding: 0;
}

.pre-login-bottom-input-container .chatgpt-send-btn:enabled {
    background: #19c37d;
    color: #fff;
}

.pre-login-bottom-input-container .chatgpt-send-btn svg {
    width: 16px;
    height: 16px;
}

/* Hide any old input structure that might still be present */
.pre-login-bottom-input-container .pre-login-input-wrapper {
    display: none !important;
}

.pre-login-bottom-input-container .pre-login-input-actions {
    display: none !important;
}

/* Ensure the new ChatGPT structure is visible */
.pre-login-bottom-input-container .chatgpt-input-wrapper {
    display: flex !important;
}

/* FORCE NEW CHATGPT LAYOUT - MAXIMUM SPECIFICITY */
html body .pre-login-container .pre-login-main .pre-login-bottom-input-container .pre-login-chat-input-form .chatgpt-input-wrapper {
    display: flex !important;
    flex-direction: column !important;
    background: var(--input-bg) !important;
    border: 3px solid red !important; /* TEST: Red border to see if CSS is applied */
    border-radius: 24px !important;
    padding: 16px !important;
    gap: 12px !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05) !important;
}

html body .pre-login-container .pre-login-main .pre-login-bottom-input-container .chatgpt-input-area {
    order: 1 !important;
    width: 100% !important;
}

html body .pre-login-container .pre-login-main .pre-login-bottom-input-container .chatgpt-tools-row {
    order: 2 !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    width: 100% !important;
}

html body .pre-login-container .pre-login-main .pre-login-bottom-input-container .chatgpt-input-area textarea {
    width: 100% !important;
    border: none !important;
    outline: none !important;
    background: transparent !important;
    resize: none !important;
    font-size: 16px !important;
    padding: 0 !important;
    min-height: 24px !important;
    max-height: 120px !important;
    color: var(--input-text) !important;
    font-family: inherit !important;
    line-height: 1.5 !important;
}

/* Legacy styles - kept for compatibility but overridden by ChatGPT styles above */
.pre-login-action-buttons {
    display: flex;
    gap: 12px;
}

.pre-login-voice-btn {
    background-color: #000000;
    color: #FFFFFF;
    border: none;
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s;
}

.pre-login-voice-btn:hover {
    background-color: #333333;
}

.pre-login-right-actions {
    display: flex;
    align-items: center;
}

.pre-login-send-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    cursor: pointer;
    transition: all 0.2s;
    padding: 0;
    overflow: hidden;
    margin-left: 8px;
}

.pre-login-send-btn:hover {
    background-color: var(--accent-hover);
    transform: none;
}

.pre-login-send-btn .send-icon {
    width: 16px;
    height: 16px;
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin: 0;
    padding: 0;
    line-height: 0;
}

/* Hide sidebar and other elements when not logged in */
html.not-logged-in .sidebar,
html.not-logged-in .chat-header,
html.not-logged-in .chat-input-container,
html.not-logged-in #toggleSidebar,
body.not-logged-in .sidebar,
body.not-logged-in .chat-header,
body.not-logged-in .chat-input-container,
body.not-logged-in #toggleSidebar {
    display: none !important;
}

html.not-logged-in .chat-container,
body.not-logged-in .chat-container {
    width: 100%;
    padding: 0;
    margin: 0;
}

html.not-logged-in .chat-messages,
body.not-logged-in .chat-messages {
    display: none;
}

/* Show pre-login UI when not logged in */
html.not-logged-in .pre-login-container,
body.not-logged-in .pre-login-container {
    display: flex;
}

/* Hide pre-login UI when logged in */
html.logged-in .pre-login-container,
body.logged-in .pre-login-container,
html:not(.not-logged-in) .pre-login-container,
body:not(.not-logged-in) .pre-login-container {
    display: none;
}

/* Pre-login chat container */
.pre-login-chat-container {
    width: 100%;
    max-width: 700px;
    margin: 20px auto;
    display: flex;
    flex-direction: column;
    gap: 16px;
    max-height: 400px;
    overflow-y: auto;
    padding: 0 24px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 5;
}

/* Pre-login message styles */
.pre-login-message {
    padding: 12px 16px;
    border-radius: 8px;
    max-width: 80%;
    word-wrap: break-word;
    line-height: 1.5;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: 8px;
}

.pre-login-user-message {
    background-color: #f0f0f0;
    color: var(--text-primary);
    align-self: flex-end;
    margin-left: auto;
    border-bottom-right-radius: 4px;
}

.pre-login-bot-message {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    align-self: flex-start;
    margin-right: auto;
    border-bottom-left-radius: 4px;
}

/* Typing indicator */
.pre-login-bot-typing {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    align-self: flex-start;
    margin-right: auto;
    padding: 12px 16px;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
}

.typing-indicator span {
    width: 8px;
    height: 8px;
    background-color: var(--text-secondary);
    border-radius: 50%;
    display: inline-block;
    opacity: 0.6;
    animation: typing-animation 1.4s infinite ease-in-out both;
}

.typing-indicator span:nth-child(1) {
    animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing-animation {
    0%, 80%, 100% {
        transform: scale(0.6);
    }
    40% {
        transform: scale(1);
    }
}

/* Pre-login notification */
.pre-login-notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(-100px);
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 16px;
    z-index: 1000;
    min-width: 300px;
    max-width: 400px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    transition: transform 0.3s ease-out;
}

.pre-login-notification.show {
    transform: translateX(-50%) translateY(0);
}

.pre-login-notification-content {
    flex: 1;
}

.pre-login-notification-title {
    font-weight: 600;
    margin-bottom: 4px;
    color: var(--text-primary);
}

.pre-login-notification-message {
    color: var(--text-secondary);
    font-size: 14px;
}

.pre-login-notification-close {
    background: none;
    border: none;
    font-size: 20px;
    line-height: 1;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0 0 0 16px;
}

.pre-login-bottom-fixed {
    position: fixed !important;
    left: 50% !important;
    bottom: 32px !important;
    top: auto !important;
    transform: translateX(-50%) !important;
    margin-top: 0 !important;
    width: 100% !important;
    max-width: 768px !important;
    padding: 0 24px !important;
    z-index: 10 !important;
}

/* Responsive styles for pre-login UI */
@media (max-width: 1200px) {
  .pre-login-welcome-message {
    max-width: 90vw;
    width: 90vw;
    padding: 20px 2vw;
  }
}

@media (max-width: 992px) {
  .pre-login-welcome-message {
    max-width: 95vw;
    width: 95vw;
    padding: 16px 2vw;
  }
  .pre-login-bottom-input-container {
    max-width: 95vw;
    width: 95vw;
    left: 2.5vw;
    right: 2.5vw;
    padding: 0 2vw;
  }
}

@media (max-width: 768px) {
  .pre-login-header, .pre-login-main, .pre-login-welcome-message, .pre-login-bottom-input-container {
    max-width: 98vw;
    width: 98vw;
    min-width: 0;
    padding: 12px;
    box-sizing: border-box;
  }
  .pre-login-welcome-message {
    max-width: 98vw;
    padding: 18px 4vw;
  }
  .pre-login-suggestion-chips, .pre-login-suggestion-chip {
    flex-wrap: wrap;
    gap: 8px;
    font-size: 13px;
  }
  .pre-login-bottom-input-container {
    position: static !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    transform: none !important;
    margin-top: 24px;
    z-index: auto;
  }
  .pre-login-bottom-input-container .chatgpt-input-wrapper {
    width: 100%;
    min-width: 0;
    box-sizing: border-box;
    padding: 12px;
    border-radius: 20px;
  }
  .pre-login-bottom-input-container .chatgpt-input-area textarea {
    font-size: 15px;
    min-height: 20px;
    max-height: 100px;
  }
  .pre-login-bottom-input-container .chatgpt-tool-btn {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
  .pre-login-bottom-input-container .chatgpt-send-btn {
    width: 28px;
    height: 28px;
  }
  .pre-login-bottom-input-container .chatgpt-send-btn svg {
    width: 14px;
    height: 14px;
  }
  .pre-login-notification {
    max-width: 95vw;
    min-width: 0;
    font-size: 0.95rem;
    padding: 8px 2vw;
  }
  .modal-content, .modal-lg {
    max-width: 98vw !important;
    width: 98vw !important;
    min-width: 0;
    max-height: 98vh !important;
    overflow-y: auto;
    border-radius: 10px;
    padding: 8px 4px;
  }
}

@media (max-width: 576px) {
  .pre-login-header, .pre-login-main, .pre-login-welcome-message, .pre-login-bottom-input-container {
    max-width: 98vw;
    padding: 4px;
    box-sizing: border-box;
  }
  .pre-login-welcome-message {
    max-width: 100vw;
    padding: 12px 2vw;
    font-size: 1rem;
  }
  .pre-login-suggestion-chips, .pre-login-suggestion-chip {
    flex-wrap: wrap;
    gap: 8px;
    font-size: 13px;
  }
  .pre-login-bottom-input-container {
    max-width: 100vw;
    padding: 0 2vw 4px 2vw;
  }
  .pre-login-bottom-input-container .chatgpt-input-wrapper {
    max-width: 98vw;
    padding: 10px;
  }
  .pre-login-bottom-input-container .chatgpt-input-area textarea {
    font-size: 15px;
    min-height: 20px;
    max-height: 100px;
  }
  .pre-login-notification {
    max-width: 98vw;
    min-width: 0;
    font-size: 0.9rem;
    padding: 6px 1vw;
  }
  .modal-content, .modal-lg {
    max-width: 100vw !important;
    width: 100vw !important;
    min-width: 0;
    max-height: 100vh !important;
    overflow-y: auto;
    border-radius: 8px;
    padding: 4px 2px;
  }
}

@media (max-width: 400px) {
  .pre-login-welcome-message {
    max-width: 100vw;
    padding: 8px 1vw;
    font-size: 0.95rem;
  }
  .pre-login-suggestion-chip {
    font-size: 12px;
    padding: 6px 6px;
  }
  .pre-login-bottom-input-container {
    padding: 0 1vw 2px 1vw;
  }
  .pre-login-bottom-input-container .chatgpt-input-area textarea {
    font-size: 14px;
  }
  .pre-login-bottom-input-container .chatgpt-input-wrapper {
    padding: 8px;
  }
}

/* Ensure modals and overlays are always visible and scrollable on all devices */
.modal, .modal-content, .modal-lg {
  max-width: 100vw;
  max-height: 100vh;
  overflow-y: auto;
  box-sizing: border-box;
}

/* Fix for input and suggestion chips on very small screens */
@media (max-width: 400px) {
  .pre-login-bottom-input-container .chatgpt-input-area textarea,
  .pre-login-welcome-message,
  .pre-login-suggestion-chip {
    font-size: 0.85rem;
  }
  .pre-login-bottom-input-container .chatgpt-input-wrapper {
    padding: 6px;
  }
  .pre-login-suggestion-chip {
    padding: 4px 4px;
  }
}

/* --- Prevent horizontal scroll on all elements --- */
body, html {
  overflow-x: hidden !important;
}
