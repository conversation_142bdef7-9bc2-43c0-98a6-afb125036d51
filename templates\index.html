<!DOCTYPE html>
<html lang="en" class="not-logged-in">
<head>
    <!-- Immediate theme apply - must be first to prevent theme flicker -->
    <script src="{{ url_for('static', filename='js/immediate-theme-apply.js') }}"></script>

    <!-- Prevent login popup on page refresh - must be second -->
    <script src="{{ url_for('static', filename='js/prevent-login-popup.js') }}"></script>

    <!-- Hide welcome message for logged-in users - must be third -->
    <script src="{{ url_for('static', filename='js/hide-welcome-for-logged-in.js') }}"></script>

    <!-- Auth state handler script - must be fourth to prevent login page flash -->
    <script src="{{ url_for('static', filename='js/auth-state-handler.js') }}"></script>

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZiaHR</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/chatgpt-style-clean.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/file-upload.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/file-manager.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/message-files.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/input-attachments.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/notifications.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/simple-toast.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/header-brand.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/light-theme-dialogs.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/pre-login-ui.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/login-modal-fix.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/search-modal-new.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/header-line-fix.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <!-- QRCode.js for client-side QR code generation -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <!-- PDF.js for PDF preview -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.min.js"></script>
    <!-- Mammoth.js for DOCX preview -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mammoth/1.4.21/mammoth.browser.min.js"></script>
</head>
<body class="theme-light not-logged-in">
    <!-- Pre-login UI -->
    <div class="pre-login-container" role="main" aria-label="Pre-login chat interface">
        <header class="pre-login-header" role="banner">
            <div class="pre-login-logo" aria-label="ZiaHR logo and brand">
                <i class="fas fa-comment-dots" aria-hidden="true"></i>
                <span>ZiaHR</span>
            </div>
            <div class="pre-login-actions">
                <button class="login-btn" id="preLoginBtn" aria-label="Log in to your account">Log in</button>
            </div>
        </header>
        <main class="pre-login-main" aria-labelledby="prelogin-welcome-heading">
            <!-- Welcome message container -->
            <div class="pre-login-welcome-container" role="region" aria-label="Welcome message and suggestions">
                <div class="pre-login-welcome-message">
                    <h2 id="prelogin-welcome-heading">👋 Welcome to ZiaHR</h2>
                    <p>I can help you with questions about company policies, employee guidelines, and HR procedures.</p>
                    <div class="pre-login-suggestion-chips" role="list" aria-label="Suggested questions">
                        <button class="pre-login-suggestion-chip" role="listitem" aria-label="Ask about leave policy" onclick="submitSuggestion('What is the company\'s leave policy?')">🗓️ Leave Policy</button>
                        <button class="pre-login-suggestion-chip" role="listitem" aria-label="Ask about referral program" onclick="submitSuggestion('How does the employee referral program work?')">👥 Referral Program</button>
                        <button class="pre-login-suggestion-chip" role="listitem" aria-label="Ask about dress code" onclick="submitSuggestion('What is the dress code policy?')">👔 Dress Code</button>
                        <button class="pre-login-suggestion-chip" role="listitem" aria-label="Ask about work from home policy" onclick="submitSuggestion('Tell me about the work from home policy')">🏠 Work from Home</button>
                    </div>
                </div>
            </div>
            <!-- Input container at the bottom -->
            <div class="pre-login-bottom-input-container" role="region" aria-label="Message input area">
                <div class="pre-login-input-wrapper">
                    <textarea class="pre-login-input" placeholder="💬 How can I help you today?" rows="1" aria-label="Type your question here" aria-multiline="true"></textarea>
                    <div class="pre-login-input-actions">
                        <div class="pre-login-action-buttons" role="group" aria-label="Input actions">
                            <button class="pre-login-action-btn" aria-label="Attach a file">
                                <i class="fas fa-paperclip" aria-hidden="true"></i>
                            </button>
                            <button class="pre-login-action-btn" aria-label="Start voice input">
                                <i class="fas fa-microphone" aria-hidden="true"></i>
                            </button>
                        </div>
                        <div class="pre-login-right-actions">
                            <button class="pre-login-send-btn" title="Send message" aria-label="Send message">
                                <img src="{{ url_for('static', filename='img/send-icon.svg') }}" alt="Send" class="send-icon">
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Main app container (shown after login) -->
    <div class="app-container">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <!-- Sidebar toggle button inside sidebar -->
            <div class="sidebar-top-controls">
                <button id="toggleSidebar" class="sidebar-toggle">
                    <i class="fas fa-bars" style="font-size: 18px;"></i>
                </button>
                <span class="sidebar-title"></span>
                <div class="sidebar-action-buttons">
                    <button id="newChatBtn" class="sidebar-action-btn" title="New Chat">
                        <!-- The correct SVG will be set by JavaScript based on theme -->
                        <img src="{{ url_for('static', filename='img/new-chat-icon-larger.svg') }}" alt="New Chat" class="new-chat-icon">
                    </button>
                    <button id="sidebarSearchBtn" class="sidebar-action-btn" title="Search">
                        <i class="fas fa-search" style="font-size: 18px;"></i>
                    </button>
                </div>
            </div>

            <nav class="sidebar-nav">
                <button id="closeSidebar" class="sidebar-close">
                    <i class="fas fa-times"></i>
                </button>

                <div class="sidebar-conversations">
                    <div class="chat-history-list" id="chatHistory"></div>
                </div>

                <div class="sidebar-bottom">
                    <!-- Upload document option removed from sidebar as per user request -->
                    <form id="uploadForm" class="upload-form">
                        <input type="file" id="fileUpload" accept=".pdf,.docx,.txt,.md" hidden multiple>
                    </form>
                    <div id="uploadStatus" class="upload-status" style="display: none;"></div>
                </div>
            </nav>
        </div>

        <!-- Main Chat Area -->
        <main class="chat-container">
            <header class="chat-header">
                <div class="header-content">
                    <!-- Left section with new chat and brand -->
                    <div class="header-left">
                        <!-- New chat button (only visible when sidebar is collapsed) -->
                        <button id="headerNewChatBtn" class="header-new-chat-btn" title="New chat">
                            <!-- The correct SVG will be set by JavaScript based on theme -->
                            <img src="{{ url_for('static', filename='img/new-chat-icon-larger.svg') }}" alt="New Chat" class="new-chat-icon">
                        </button>

                        <!-- ZiaHR branding -->
                        <div class="header-brand">
                            <h1 class="brand-name">ZiaHR</h1>
                        </div>
                    </div>

                    <div class="header-actions">
                        <!-- Dark mode toggle removed - only available in settings -->
                        <!-- User account button -->
                        <div class="user-account-dropdown">
                            <button id="userAccountBtn" class="header-action-btn login-icon" title="Account">
                                <i class="fas fa-user-circle"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            <div class="chat-messages" id="chatMessages">
                <!-- Dynamic greeting message - positioned in the middle of the page -->
                <div class="greeting-message-container" id="greetingMessageContainer" style="display: none;">
                    <div class="welcome-message">
                        <h2 id="welcomeTitleDynamic" class="greeting-message"></h2>
                        <p>I can help you with questions about company policies, employee guidelines, and HR procedures.</p>
                        <div class="suggestion-chips">
                            <button class="suggestion-chip" onclick="submitSuggestion('What is the company\'s leave policy?')">🗓️ Leave Policy</button>
                            <button class="suggestion-chip" onclick="submitSuggestion('How does the employee referral program work?')">👥 Referral Program</button>
                            <button class="suggestion-chip" onclick="submitSuggestion('What is the dress code policy?')">👔 Dress Code</button>
                            <button class="suggestion-chip" onclick="submitSuggestion('Tell me about the work from home policy')">🏠 Work from Home</button>
                        </div>
                    </div>
                </div>
                <!-- Welcome container - only shown for non-logged-in users -->
                <div class="welcome-container not-logged-in-only">
                    <div class="welcome-message">
                        <h2 id="welcomeTitle">👋 Welcome to ZiaHR</h2>
                        <p>I can help you with questions about company policies, employee guidelines, and HR procedures.</p>
                        <div class="suggestion-chips">
                            <button class="suggestion-chip" onclick="submitSuggestion('What is the company\'s leave policy?')">🗓️ Leave Policy</button>
                            <button class="suggestion-chip" onclick="submitSuggestion('How does the employee referral program work?')">👥 Referral Program</button>
                            <button class="suggestion-chip" onclick="submitSuggestion('What is the dress code policy?')">👔 Dress Code</button>
                            <button class="suggestion-chip" onclick="submitSuggestion('Tell me about the work from home policy')">🏠 Work from Home</button>
                        </div>
                    </div>
                </div>
            </div>

            <footer class="chat-input-container">
                <form id="chatForm" class="chat-input-form">
                    <div class="chatgpt-input-wrapper">
                        <!-- Main textarea at top -->
                        <div class="chatgpt-input-area">
                            <textarea id="userInput" placeholder="Ask anything" rows="1" autocomplete="off"></textarea>
                        </div>

                        <!-- Bottom tools section with only our 3 functionalities -->
                        <div class="chatgpt-bottom-tools">
                            <div class="chatgpt-left-tools">
                                <button type="button" id="quickUploadBtn" class="chatgpt-tool-btn" title="Upload Documents">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <input type="file" id="quickFileUpload" accept=".pdf,.docx,.txt,.md" hidden multiple>
                                <button type="button" id="inlineEscalationIconContainer" class="chatgpt-tool-btn" title="Escalate to HR" style="display: none;">
                                    <i class="fas fa-exclamation-circle" style="color: #dc3545;"></i>
                                </button>
                            </div>

                            <div class="chatgpt-right-tools">
                                <!-- Only voice input -->
                                <button type="button" id="voiceInputBtn" class="chatgpt-tool-btn" title="Voice Input">
                                    <i class="fas fa-microphone"></i>
                                </button>
                                <!-- Send button (appears when typing) -->
                                <button type="submit" id="sendBtn" class="chatgpt-send-btn" title="Send message" disabled style="display: none;">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M7 11L12 6L17 11M12 18V7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- File list container (hidden, used for internal tracking) -->
                    <div id="fileListContainer" class="file-list-container" style="display: none;"></div>

                    <!-- Input attachments container (for showing files in input area) -->
                    <div id="inputAttachmentsContainer" class="input-attachments-container" style="display: none;"></div>

                    <!-- File display area -->
                    <div class="file-display-area">
                        <!-- Files will be dynamically added here by JavaScript -->
                    </div>
                </form>
            </footer>
        </main>

        <!-- Source Panel - Hidden as per user request -->
        <aside class="source-panel" id="sourcePanel" style="display: none;">
            <div class="source-panel-header">
                <h3>Sources</h3>
                <button id="closeSourcePanel" class="icon-button">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="source-panel-content" id="sourcePanelContent">
                <p>No sources available for this response.</p>
            </div>
        </aside>
    </div>

    <!-- Voice Recording Modal -->
    <div class="modal" id="voiceModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Voice Input</h3>
                <button id="closeVoiceModal" class="icon-button">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="recording-indicator">
                    <div class="recording-waves">
                        <span></span>
                        <span></span>
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                    <p id="recordingStatus">Listening...</p>
                </div>
                <div class="recording-controls">
                    <button id="startRecording" class="record-btn record-start-btn">
                        <img src="{{ url_for('static', filename='img/mic-icon-new.svg') }}" alt="Microphone" class="record-btn-icon"> Start
                    </button>
                    <button id="stopRecording" class="record-btn record-stop-btn" disabled>
                        <i class="fas fa-stop"></i> Stop
                    </button>
                </div>
                <div id="transcriptionResult" class="transcription-result" contenteditable="false" spellcheck="true" placeholder="Your speech will appear here..."></div>
            </div>
            <div class="modal-footer">
                <button id="cancelVoiceInput" class="btn btn-secondary">Cancel</button>
                <button id="submitVoiceInput" class="btn btn-primary" disabled>Submit</button>
            </div>
        </div>
    </div>

    <!-- File Preview Modal -->
    <div class="modal" id="filePreviewModal">
        <div class="modal-content modal-lg">
            <div class="modal-header">
                <h3 id="previewFileName">File Preview</h3>
                <button id="closeFilePreviewModal" class="icon-button">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="filePreviewContainer" class="file-preview-container">
                    <!-- Preview content will be inserted here -->
                </div>
            </div>
            <div class="modal-footer">
                <button id="closePreview" class="btn btn-secondary">Close</button>
            </div>
        </div>
    </div>

    <!-- Login Modal -->
    <div class="modal" id="loginModal">
        <div class="modal-content modern-login">
            <div class="modal-header">
                <h3>Log In</h3>
                <button id="closeLoginModal" class="icon-button">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="login-form">
                    <div class="form-group">
                        <label for="username">Email Address</label>
                        <input type="email" id="username" class="form-control" placeholder="Enter your email">
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <div class="password-input-wrapper">
                            <input type="password" id="password" class="form-control" placeholder="Enter your password">
                            <button type="button" class="password-toggle-btn" tabindex="-1">
                                <i class="fas fa-eye password-icon"></i>
                            </button>
                        </div>
                    </div>
                    <div class="form-group forgot-password-container">
                        <a href="#" class="forgot-password" id="forgotPassword">Forgot password?</a>
                    </div>
                    <div id="loginMessage" class="login-message" style="display: none;"></div>
                    <button id="submitLogin" class="btn btn-primary btn-block">Sign In</button>
                    <div class="login-actions-container">
                        <button id="registerLink" class="btn btn-text">Create Account</button>
                    </div>
                    <div class="register-link-container" style="display: none;">
                        <a href="#" id="registerLinkHidden">Register</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 2FA Modal -->
    <div class="modal" id="twoFAModal" style="display: none;">
      <div class="modal-content modern-login">
        <div class="modal-header">
          <h3>Two-Factor Authentication</h3>
          <button id="closeTwoFAModal" class="icon-button">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="login-form" id="twoFAQRSection">
            <div class="form-group">
              <label>Scan this QR code with your authenticator app:</label>
              <div style="text-align:center; margin: 16px 0;">
                <img id="twoFAQRImage" src="" alt="2FA QR Code" style="max-width: 200px; display: block; margin: 0 auto;" />
              </div>
            </div>
            <button id="nextTo2FAInput" class="btn btn-primary btn-block">Next</button>
          </div>
          <div class="login-form" id="twoFACodeSection" style="display:none;">
            <div class="form-group">
              <label for="twoFACode">Enter 2FA Code</label>
              <input type="text" id="twoFACode" class="form-control" placeholder="Enter your 2FA code">
            </div>
            <div id="twoFAMessage" class="login-message" style="display: none;"></div>
            <button id="submitTwoFA" class="btn btn-primary btn-block">Verify</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Registration Modal -->
    <div class="modal" id="registerModal">
        <div class="modal-content modern-login">
            <div class="modal-header">
                <h3>Register</h3>
                <button id="closeRegisterModal" class="icon-button">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="login-form">
                    <div class="form-group">
                        <label for="fullName">Full Name<span class="required">*</span></label>
                        <input type="text" id="fullName" class="form-control" placeholder="Enter your full name">
                    </div>
                    <div class="form-group">
                        <label for="registerEmail">Email Address<span class="required">*</span></label>
                        <input type="email" id="registerEmail" class="form-control" placeholder="Enter your email">
                    </div>
                    <div class="form-group">
                        <label for="registerPassword">Password<span class="required">*</span></label>
                        <div class="password-input-wrapper">
                            <input type="password" id="registerPassword" class="form-control" placeholder="Create a password">
                            <button type="button" class="password-toggle-btn" tabindex="-1">
                                <i class="fas fa-eye password-icon"></i>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="employeeId">Employee ID</label>
                        <input type="text" id="employeeId" class="form-control" placeholder="Enter your employee ID (Optional)">
                    </div>
                    <div id="registerMessage" class="login-message" style="display: none;"></div>
                    <button id="submitRegister" class="btn btn-primary btn-block">Create Account</button>
                    <div class="login-actions-container">
                        <button id="loginLink" class="btn btn-text">Sign In Instead</button>
                    </div>
                    <div class="register-link-container" style="display: none;">
                        <a href="#" id="loginLinkHidden">Log In</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div class="modal" id="settingsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Settings</h3>
                <button id="closeSettingsModal" class="icon-button">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="settings-layout">
                    <div class="settings-sidebar">
                        <div class="settings-nav-item active" data-tab="general">
                            <i class="fas fa-cog"></i> General
                        </div>
                        <div class="settings-nav-item" data-tab="security">
                            <i class="fas fa-shield-alt"></i> Security
                        </div>
                        <div class="settings-nav-item" data-tab="personal">
                            <i class="fas fa-user"></i> Personal
                        </div>
                    </div>
                    <div class="settings-content">
                        <!-- General Settings Tab -->
                        <div class="settings-panel active" id="general-panel">
                            <div class="settings-item">
                                <div class="settings-item-left">Theme</div>
                                <div class="settings-item-right">
                                    <select id="themeSelect" class="dropdown-select">
                                        <option value="system">System</option>
                                        <option value="light">Light</option>
                                        <option value="dark">Dark</option>
                                    </select>
                                </div>
                            </div>

                            <div class="settings-item">
                                <div class="settings-item-left">Language</div>
                                <div class="settings-item-right">
                                    <select id="languageSelect" class="dropdown-select">
                                        <option value="auto-detect">Auto-detect</option>
                                        <option value="en">English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                    </select>
                                </div>
                            </div>

                            <div class="settings-item">
                                <div class="settings-item-left">Archived chats</div>
                                <div class="settings-item-right">
                                    <button id="manageArchivedChats" class="settings-button">Manage</button>
                                </div>
                            </div>

                            <div class="settings-item">
                                <div class="settings-item-left">Archive all chats</div>
                                <div class="settings-item-right">
                                    <button id="archiveAllChats" class="settings-button">Archive all</button>
                                </div>
                            </div>

                            <div class="settings-item">
                                <div class="settings-item-left">Delete all chats</div>
                                <div class="settings-item-right">
                                    <button id="deleteAllChats" class="settings-button danger">Delete all</button>
                                </div>
                            </div>

                            <div class="settings-item">
                                <div class="settings-item-left">Log out on this device</div>
                                <div class="settings-item-right">
                                    <button id="logoutDevice" class="settings-button logout-button">Log out</button>
                                </div>
                            </div>
                        </div>

                        <!-- Security Settings Tab -->
                        <div class="settings-panel" id="security-panel">
                            <div class="settings-item">
                                <div class="settings-item-left">Multi-factor authentication</div>
                                <div class="settings-item-right">
                                    <button id="enableMFA" class="settings-button">Enable</button>
                                </div>
                            </div>
                            <div class="settings-description">
                                <p>Require an extra security challenge when logging in. If you are unable to pass this challenge, you will have the option to recover your account via email.</p>
                            </div>

                            <div class="settings-item">
                                <div class="settings-item-left">Log out of all devices</div>
                                <div class="settings-item-right">
                                    <button id="logoutAllDevices" class="settings-button">Log out all</button>
                                </div>
                            </div>
                            <div class="settings-description">
                                <p>Log out of all active sessions across all devices, including your current session. It may take up to 30 minutes for other devices to be logged out.</p>
                            </div>
                        </div>

                        <!-- Personal Settings Tab -->
                        <div class="settings-panel" id="personal-panel">
                            <div class="settings-section">
                                <h4>Personal Information</h4>
                                <div class="settings-item">
                                    <div class="settings-item-left">Full Name</div>
                                    <div class="settings-item-right">
                                        <input type="text" id="personalFullName" class="form-control" placeholder="Enter your full name">
                                    </div>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-left">Email Address</div>
                                    <div class="settings-item-right">
                                        <input type="email" id="personalEmail" class="form-control" placeholder="Enter your email">
                                    </div>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-left">Employee ID</div>
                                    <div class="settings-item-right">
                                        <input type="text" id="personalEmployeeId" class="form-control" placeholder="Enter your employee ID">
                                    </div>
                                </div>
                            </div>
                            <!-- Add more personal settings fields here if needed -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button id="saveSettingsBtn" class="btn btn-primary">Save Changes</button>
            </div>
        </div>
    </div>

    <!-- Analytics Dashboard Modal removed -->

    <!-- Archived Chats Modal -->
    <div class="modal" id="archivedChatsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Archived Chats</h3>
                <button id="closeArchivedChatsModal" class="icon-button">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="archived-chats-list" id="archivedChatsList">
                    <!-- Archived chats will be loaded here -->
                    <div class="loading-spinner">Loading archived chats...</div>
                </div>
                <div class="no-archived-chats-message" style="display: none;">
                    <p>You don't have any archived chats yet.</p>
                    <p>When you archive a chat, it will appear here.</p>
                </div>
            </div>
            <!-- Modal footer removed as requested -->
        </div>
    </div>

    <!-- Include the escalation form template -->
    {% include 'escalation_form.html' %}

    <!-- Add the escalation manager script -->
    <script src="{{ url_for('static', filename='js/escalation-manager.js') }}"></script>

    <!-- Add Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <script src="{{ url_for('static', filename='js/toast-notifications.js') }}"></script>
    <script src="{{ url_for('static', filename='js/chatgpt-ui-clean.js') }}"></script>
    <script src="{{ url_for('static', filename='js/speech-recognition.js') }}"></script>
    <script src="{{ url_for('static', filename='js/file-manager.js') }}"></script>
    <script src="{{ url_for('static', filename='js/user-account.js') }}"></script>
    <script src="{{ url_for('static', filename='js/app-integration.js') }}"></script>
    <script src="{{ url_for('static', filename='js/settings-manager.js') }}"></script>
    <script src="{{ url_for('static', filename='js/hide-slider-homepage.js') }}"></script>
    <script src="{{ url_for('static', filename='js/pre-login-ui.js') }}"></script>

    <script src="{{ url_for('static', filename='js/remove-debug.js') }}"></script>
    <script src="{{ url_for('static', filename='js/send-button-icons.js') }}"></script>
    <script src="{{ url_for('static', filename='js/remove-welcome-after-login.js') }}"></script>
    <script src="{{ url_for('static', filename='js/dynamic-greeting.js') }}"></script>
    <script src="{{ url_for('static', filename='js/archived-chats-modal.js') }}"></script>
    <script src="{{ url_for('static', filename='js/duplicate-elements-check.js') }}"></script>
    <script src="{{ url_for('static', filename='js/new-chat-handler.js') }}"></script>
    <script src="{{ url_for('static', filename='js/search-modal-fix.js') }}"></script>
    <script src="{{ url_for('static', filename='js/header-line-fix.js') }}"></script>
    <script src="{{ url_for('static', filename='js/header-controls.js') }}"></script>
    <script src="{{ url_for('static', filename='js/init-new-chat-icons.js') }}"></script>

</body>
</html>
